// Core AI provider types
export type AIProviderType = 'gemini' | 'openai' | 'custom';
export type StatusType = 'info' | 'success' | 'error';

// API configuration for all providers
export interface ApiKeys {
    gemini: string;
    openai: string;
    customUrl: string;
    customKey: string;
}

// Text alignment options
export type TextAlignType = 'left' | 'center' | 'right' | 'justify';

// Line spacing options
export type LineSpacingType = '1.0' | '1.15' | '1.5' | '2.0' | '2.5' | '3.0';

// System prompts for AI features
export interface SystemPrompts {
    autocomplete: string;
    replaceSelection: string;
    insertAtCursor: string;
}

// Main application settings
export interface AppSettings {
    fontFamily: string;
    fontSize: string;
    textAlign: TextAlignType;
    lineSpacing: LineSpacingType;
    aiProvider: AIProviderType;
    aiModel: string;
    autocompleteContextLength: number;
    temperature: number;
    topP: number;
    sidebarVisible: boolean;
    darkMode: boolean;
    showThinkingPanel: boolean;
    systemPrompts: SystemPrompts;
}

// AI provider interface - all adapters must implement this
export interface AIProvider {
    generate(prompt: string): Promise<string>;
}

// Status message for user feedback
export interface StatusMessage {
    text: string;
    type: StatusType;
    id: number;
}

// Model history tracking for custom models
export interface ModelHistoryItem {
    model: string;
    lastUsed: number;
}

// Custom model history organized by provider
export interface CustomModelHistory {
    [provider: string]: ModelHistoryItem[];
}

// Provider-specific models for state restoration
export interface ProviderSpecificModels {
    gemini: string;
    openai: string;
    custom: string;
}
