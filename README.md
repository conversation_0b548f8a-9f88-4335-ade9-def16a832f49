# AI Notepad

A simple, clean, and powerful notepad with AI-powered features.

## Features

- **📝 Simple & Clean Interface**: A minimalist notepad for focused writing.
- **🤖 AI-Powered Features**:
    - **Autocomplete**: Let the AI continue your thoughts.
    - **Replace Selection**: Highlight a section and have the AI fill in the blanks.
    - **Insert at Cursor**: Get AI-generated text right where you need it.
- **🧠 Thinking Process**: See the AI's reasoning behind its suggestions.
    - **Custom Providers**: Support for `<think>` tags in responses.
    - **Gemini 2.5 Models**: Native thinking support with automatic detection.
- **⚙️ Customizable**:
    - **Font**: Choose your favorite font and size.
    - **AI Provider**: Switch between OpenAI, Google Gemini, and custom OpenAI-compatible endpoints.
    - **Model**: Select the best model for your needs.
- **🔒 Secure**: Your API keys are stored locally in your browser and never leave your machine.
- **💾 Auto-Save**: Your work is automatically saved to your browser's local storage.

## Tech Stack

- **SvelteKit**: A fast and modern web framework for building user interfaces.
- **TypeScript**: For type safety and a better developer experience.
- **Vite**: A next-generation front-end tooling that provides a faster and leaner development experience.
- **Vitest**: A blazing fast unit-test framework powered by Vite.

## Getting Started

1.  **Clone the repository**:
    ```bash
    git clone https://github.com/your-username/ai-notepad.git
    ```
2.  **Install dependencies**:
    ```bash
    cd ai-notepad
    npm install
    ```
3.  **Run the development server**:
    ```bash
    npm run dev
    ```
4.  **Open the app**:
    Open your browser and navigate to `http://localhost:5173`.

## Configuration

1.  **API Keys**:
    - Click on the "🔑 API Keys" button in the header.
    - Add your API keys for OpenAI, Gemini, or your custom provider.
2.  **Settings**:
    - Use the sidebar to configure your preferred font, AI provider, and model.

## Contributing

Contributions are welcome! Please feel free to submit a pull request or open an issue.
