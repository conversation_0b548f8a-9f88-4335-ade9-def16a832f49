// Test script to verify AI provider and model persistence
// Run this in the browser console on the main app page

console.log('🧪 Starting AI Persistence Test');

// Helper function to wait for settings to load
function waitForSettings() {
    return new Promise((resolve) => {
        const checkSettings = () => {
            const unified = localStorage.getItem('aiNotepadSvelteUnifiedSettings');
            if (unified) {
                resolve(JSON.parse(unified));
            } else {
                setTimeout(checkSettings, 100);
            }
        };
        checkSettings();
    });
}

// Test 1: Set specific provider/model combinations and verify persistence
async function testProviderModelPersistence() {
    console.log('\n📋 Test 1: Provider/Model Persistence');
    
    // Clear storage first
    localStorage.removeItem('aiNotepadSvelteUnifiedSettings');
    console.log('🧹 Cleared storage');
    
    // Set test data
    const testData = {
        appSettings: {
            fontFamily: 'Arial, sans-serif',
            fontSize: '16',
            textAlign: 'left',
            lineSpacing: '1.5',
            aiProvider: 'gemini',
            aiModel: 'gemini-1.5-pro',
            autocompleteContextLength: 1000,
            temperature: 0.7,
            topP: 0.9,
            sidebarVisible: true,
            darkMode: false,
            showThinkingPanel: true,
            systemPrompts: {
                autocomplete: 'Complete the following text naturally:',
                replaceSelection: 'Replace the selected text with an improved version:',
                insertAtCursor: 'Generate relevant content for insertion:'
            }
        },
        apiKeys: {
            gemini: '',
            openai: '',
            customUrl: '',
            customKey: ''
        },
        notepadContent: '',
        customModelHistory: { gemini: [], openai: [], custom: [] },
        providerSpecificModels: {
            gemini: 'gemini-1.5-pro',
            openai: 'gpt-4o',
            custom: 'my-custom-model'
        }
    };
    
    localStorage.setItem('aiNotepadSvelteUnifiedSettings', JSON.stringify(testData));
    console.log('💾 Set test data:', testData);
    
    console.log('🔄 Please reload the page now and check:');
    console.log('1. Provider should be "Gemini"');
    console.log('2. Model should be "Gemini 1.5 Pro"');
    console.log('3. Switch to OpenAI - should show "GPT-4o"');
    console.log('4. Switch to Custom - should show "my-custom-model"');
    console.log('5. Switch back to Gemini - should show "Gemini 1.5 Pro"');
}

// Test 2: Verify cross-contamination doesn't occur
async function testProviderIsolation() {
    console.log('\n🔒 Test 2: Provider Isolation');
    
    const testData = {
        appSettings: {
            fontFamily: 'Arial, sans-serif',
            fontSize: '16',
            textAlign: 'left',
            lineSpacing: '1.5',
            aiProvider: 'openai',
            aiModel: 'gpt-4o',
            autocompleteContextLength: 1000,
            temperature: 0.7,
            topP: 0.9,
            sidebarVisible: true,
            darkMode: false,
            showThinkingPanel: true,
            systemPrompts: {
                autocomplete: 'Complete the following text naturally:',
                replaceSelection: 'Replace the selected text with an improved version:',
                insertAtCursor: 'Generate relevant content for insertion:'
            }
        },
        apiKeys: {
            gemini: '',
            openai: '',
            customUrl: '',
            customKey: ''
        },
        notepadContent: '',
        customModelHistory: { gemini: [], openai: [], custom: [] },
        providerSpecificModels: {
            gemini: 'gemini-2.5-flash',
            openai: 'gpt-4o',
            custom: ''
        }
    };
    
    localStorage.setItem('aiNotepadSvelteUnifiedSettings', JSON.stringify(testData));
    console.log('💾 Set test data with OpenAI as current provider');
    
    console.log('🔄 Please reload the page and verify:');
    console.log('1. Provider should be "OpenAI"');
    console.log('2. Model should be "GPT-4o"');
    console.log('3. Switch to Gemini - should show "Gemini 2.5 Flash" (NOT a GPT model)');
    console.log('4. Switch back to OpenAI - should show "GPT-4o" (NOT a Gemini model)');
}

// Test 3: Check current state
function checkCurrentState() {
    console.log('\n🔍 Current State Check');
    
    const unified = localStorage.getItem('aiNotepadSvelteUnifiedSettings');
    if (unified) {
        const parsed = JSON.parse(unified);
        console.log('📊 Current Settings:');
        console.log('Provider:', parsed.appSettings.aiProvider);
        console.log('Current Model:', parsed.appSettings.aiModel);
        console.log('Provider-Specific Models:', parsed.providerSpecificModels);
        
        // Check for cross-contamination
        const { providerSpecificModels } = parsed;
        const geminiModels = ['gemini-2.5-flash', 'gemini-2.5-pro', 'gemini-2.0-flash', 'gemini-1.5-flash', 'gemini-1.5-pro'];
        const openaiModels = ['gpt-4o-mini', 'gpt-4o', 'gpt-3.5-turbo'];
        
        let contamination = false;
        
        // Check if Gemini has OpenAI models
        if (openaiModels.includes(providerSpecificModels.gemini)) {
            console.error('❌ CONTAMINATION: Gemini provider has OpenAI model:', providerSpecificModels.gemini);
            contamination = true;
        }
        
        // Check if OpenAI has Gemini models
        if (geminiModels.includes(providerSpecificModels.openai)) {
            console.error('❌ CONTAMINATION: OpenAI provider has Gemini model:', providerSpecificModels.openai);
            contamination = true;
        }
        
        if (!contamination) {
            console.log('✅ No cross-contamination detected');
        }
        
    } else {
        console.log('❌ No unified settings found');
    }
}

// Export functions for manual testing
window.testAIPersistence = {
    testProviderModelPersistence,
    testProviderIsolation,
    checkCurrentState
};

console.log('🎯 Test functions available:');
console.log('- testAIPersistence.testProviderModelPersistence()');
console.log('- testAIPersistence.testProviderIsolation()');
console.log('- testAIPersistence.checkCurrentState()');
console.log('\nRun checkCurrentState() first to see current state');
