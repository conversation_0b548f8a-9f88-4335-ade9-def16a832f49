<script lang="ts">
  import { appSettings, updateSystemPrompt } from '../stores/unifiedSettingsStore';

  // Component props
  export let isOpen = false;
  export let close: () => void;

  let activeTab: 'autocomplete' | 'replaceSelection' | 'insertAtCursor' = 'autocomplete';

  // Close modal and call close callback
  const closeModal = () => {
    isOpen = false;
    close();
  };

  // Handle backdrop click to close
  const handleBackdropClick = (event: MouseEvent) => {
    if (event.target === event.currentTarget) {
      closeModal();
    }
  };

  // Handle escape key to close
  const handleKeydown = (event: KeyboardEvent) => {
    if (event.key === 'Escape') {
      closeModal();
    }
  };

  // Auto-save system prompt changes
  const handleInput = (promptType: keyof typeof $appSettings.systemPrompts) => (event: Event) => {
    const target = event.target as HTMLTextAreaElement;
    updateSystemPrompt(promptType, target.value);
  };
</script>

<svelte:window on:keydown={handleKeydown} />

{#if isOpen}
  <!-- Modal backdrop with click-to-close -->
  <div
    class="modal-backdrop"
    role="dialog"
    aria-modal="true"
    tabindex="-1"
    on:click={handleBackdropClick}
    on:keydown={handleKeydown}
  >
    <div class="modal-content">
      <!-- Modal header -->
      <div class="modal-header">
        <h2>System Prompts Configuration</h2>
        <button class="close-button" on:click={closeModal} aria-label="Close modal">
          ×
        </button>
      </div>

      <div class="modal-body">
        <!-- Prompt type tabs -->
        <div class="tabs">
          <button
            class="tab-button"
            class:active={activeTab === 'autocomplete'}
            on:click={() => activeTab = 'autocomplete'}
          >
            Autocomplete
          </button>
          <button
            class="tab-button"
            class:active={activeTab === 'replaceSelection'}
            on:click={() => activeTab = 'replaceSelection'}
          >
            Replace Selection
          </button>
          <button
            class="tab-button"
            class:active={activeTab === 'insertAtCursor'}
            on:click={() => activeTab = 'insertAtCursor'}
          >
            Insert at Cursor
          </button>
        </div>

        <!-- Tab content for each prompt type -->
        <div class="tab-content">
          {#if activeTab === 'autocomplete'}
            <div class="prompt-section">
              <h3>Autocomplete System Prompt</h3>
              <div class="input-group">
                <label for="autocomplete-prompt">System Prompt:</label>
                <textarea
                  id="autocomplete-prompt"
                  placeholder="Enter the system prompt for autocomplete functionality"
                  bind:value={$appSettings.systemPrompts.autocomplete}
                  on:input={handleInput('autocomplete')}
                  rows="8"
                ></textarea>
                <small>This prompt guides the AI when continuing text from the current cursor position.</small>
              </div>
            </div>
          {/if}

          {#if activeTab === 'replaceSelection'}
            <div class="prompt-section">
              <h3>Replace Selection System Prompt</h3>
              <div class="input-group">
                <label for="replace-prompt">System Prompt:</label>
                <textarea
                  id="replace-prompt"
                  placeholder="Enter the system prompt for replace selection functionality"
                  bind:value={$appSettings.systemPrompts.replaceSelection}
                  on:input={handleInput('replaceSelection')}
                  rows="8"
                ></textarea>
                <small>This prompt guides the AI when replacing selected text with new content.</small>
              </div>
            </div>
          {/if}

          {#if activeTab === 'insertAtCursor'}
            <div class="prompt-section">
              <h3>Insert at Cursor System Prompt</h3>
              <div class="input-group">
                <label for="insert-prompt">System Prompt:</label>
                <textarea
                  id="insert-prompt"
                  placeholder="Enter the system prompt for insert at cursor functionality"
                  bind:value={$appSettings.systemPrompts.insertAtCursor}
                  on:input={handleInput('insertAtCursor')}
                  rows="8"
                ></textarea>
                <small>This prompt guides the AI when inserting text at the current cursor position.</small>
              </div>
            </div>
          {/if}
        </div>
      </div>
    </div>
  </div>
{/if}

<style>
  .modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--color-modal-backdrop);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    transition: background-color 0.2s ease;
  }

  .modal-content {
    background: var(--color-modal-bg);
    border-radius: 8px;
    width: 90%;
    max-width: 700px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 4px 20px var(--color-shadow);
    transition: all 0.2s ease;
  }

  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid var(--color-border);
    background-color: var(--color-modal-header);
    transition: all 0.2s ease;
  }

  .modal-header h2 {
    margin: 0;
    color: var(--color-text-primary);
    font-size: 1.25rem;
  }

  .close-button {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: var(--color-text-primary);
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
  }

  .close-button:hover {
    background-color: var(--color-toggle-hover);
    color: var(--color-text-primary);
  }

  .modal-body {
    padding: 0;
  }

  .tabs {
    display: flex;
    border-bottom: 1px solid var(--color-border);
    background-color: var(--color-modal-header);
    transition: all 0.2s ease;
  }

  .tab-button {
    flex: 1;
    padding: 15px 20px;
    border: none;
    background: none;
    color: var(--color-text-secondary);
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.2s ease;
    border-bottom: 3px solid transparent;
  }

  .tab-button:hover {
    background-color: var(--color-toggle-hover);
    color: var(--color-text-primary);
  }

  .tab-button.active {
    color: var(--color-text-primary);
    background-color: var(--color-modal-bg);
    border-bottom-color: var(--color-accent);
  }

  .tab-content {
    padding: 20px;
    max-height: 60vh;
    overflow-y: auto;
  }

  .prompt-section h3 {
    margin: 0 0 20px 0;
    color: var(--color-text-primary);
    font-size: 1.1rem;
  }

  .input-group {
    margin-bottom: 20px;
  }

  .input-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
    color: var(--color-text-primary);
    font-size: 0.9rem;
  }

  .input-group textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--color-border);
    border-radius: 4px;
    font-size: 0.9rem;
    background-color: var(--color-input-bg);
    color: var(--color-text-primary);
    resize: vertical;
    min-height: 120px;
    font-family: 'Courier New', Courier, monospace;
    line-height: 1.4;
    transition: all 0.2s ease;
  }

  .input-group textarea:focus {
    outline: none;
    border-color: var(--color-accent);
    box-shadow: 0 0 0 2px var(--color-accent-alpha);
  }

  .input-group small {
    display: block;
    margin-top: 8px;
    color: var(--color-text-secondary);
    font-size: 0.8rem;
    line-height: 1.4;
  }
</style>
