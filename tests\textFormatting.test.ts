import { describe, it, expect, beforeEach } from 'vitest';
import { get } from 'svelte/store';
import { appSettings, initializeAllSettings, updateAppSetting } from '../src/lib/stores/unifiedSettingsStore';
import { DEFAULT_APP_SETTINGS } from '../src/lib/config';

describe('Text Formatting Settings', () => {
    beforeEach(async () => {
        // Reset the store before each test
        appSettings.set({
            fontFamily: 'Arial, sans-serif',
            fontSize: '16',
            textAlign: 'left',
            lineSpacing: '1.5',
            aiProvider: 'openai',
            aiModel: 'gpt-4o-mini',
            autocompleteContextLength: 1000,
            sidebarVisible: true,
            darkMode: false,
            showThinkingPanel: true,
        });
        await initializeAllSettings();
    });

    describe('Text Alignment Settings', () => {
        it('should have default text alignment as left', () => {
            const settings = get(appSettings);
            expect(settings.textAlign).toBe('left');
        });

        it('should update text alignment to center', () => {
            updateAppSetting('textAlign', 'center');
            const settings = get(appSettings);
            expect(settings.textAlign).toBe('center');
        });

        it('should update text alignment to right', () => {
            updateAppSetting('textAlign', 'right');
            const settings = get(appSettings);
            expect(settings.textAlign).toBe('right');
        });

        it('should update text alignment to justify', () => {
            updateAppSetting('textAlign', 'justify');
            const settings = get(appSettings);
            expect(settings.textAlign).toBe('justify');
        });

        it('should preserve other settings when changing text alignment', () => {
            const originalFontSize = get(appSettings).fontSize;
            updateAppSetting('textAlign', 'center');
            const settings = get(appSettings);
            expect(settings.fontSize).toBe(originalFontSize);
            expect(settings.textAlign).toBe('center');
        });
    });

    describe('Line Spacing Settings', () => {
        it('should have default line spacing as 1.5', () => {
            const settings = get(appSettings);
            expect(settings.lineSpacing).toBe('1.5');
        });

        it('should update line spacing to single (1.0)', () => {
            updateAppSetting('lineSpacing', '1.0');
            const settings = get(appSettings);
            expect(settings.lineSpacing).toBe('1.0');
        });

        it('should update line spacing to double (2.0)', () => {
            updateAppSetting('lineSpacing', '2.0');
            const settings = get(appSettings);
            expect(settings.lineSpacing).toBe('2.0');
        });

        it('should update line spacing to triple (3.0)', () => {
            updateAppSetting('lineSpacing', '3.0');
            const settings = get(appSettings);
            expect(settings.lineSpacing).toBe('3.0');
        });

        it('should preserve other settings when changing line spacing', () => {
            const originalFontFamily = get(appSettings).fontFamily;
            updateAppSetting('lineSpacing', '2.0');
            const settings = get(appSettings);
            expect(settings.fontFamily).toBe(originalFontFamily);
            expect(settings.lineSpacing).toBe('2.0');
        });
    });

    describe('Default Settings Integration', () => {
        it('should include text formatting in default settings', () => {
            expect(DEFAULT_APP_SETTINGS.textAlign).toBe('left');
            expect(DEFAULT_APP_SETTINGS.lineSpacing).toBe('1.5');
        });

        it('should maintain all required properties in app settings', () => {
            const settings = get(appSettings);
            expect(settings).toHaveProperty('textAlign');
            expect(settings).toHaveProperty('lineSpacing');
            expect(settings).toHaveProperty('fontFamily');
            expect(settings).toHaveProperty('fontSize');
        });
    });

    describe('Settings Persistence', () => {
        it('should persist text alignment changes', () => {
            updateAppSetting('textAlign', 'center');
            updateAppSetting('lineSpacing', '2.0');
            
            const settings = get(appSettings);
            expect(settings.textAlign).toBe('center');
            expect(settings.lineSpacing).toBe('2.0');
        });

        it('should handle multiple formatting changes in sequence', () => {
            updateAppSetting('textAlign', 'right');
            updateAppSetting('lineSpacing', '1.0');
            updateAppSetting('textAlign', 'justify');
            updateAppSetting('lineSpacing', '3.0');
            
            const settings = get(appSettings);
            expect(settings.textAlign).toBe('justify');
            expect(settings.lineSpacing).toBe('3.0');
        });
    });
});
