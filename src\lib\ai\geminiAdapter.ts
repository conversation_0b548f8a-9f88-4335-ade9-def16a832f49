import { AIProviderAdapter } from './aiProvider';

/**
 * Adapter for the Google Gemini API.
 */
export class <PERSON><PERSON><PERSON>pter extends AIProviderAdapter {
    /**
     * Checks if the current model supports thinking capabilities.
     * @returns True if the model supports thinking, false otherwise.
     */
    private isThinkingSupported(): boolean {
        // Thinking is supported on Gemini 2.5 series models
        return this.model.includes('2.5');
    }

    /**
     * Generates a response from the Gemini API.
     * @param prompt The prompt to send to the AI.
     * @returns A promise that resolves to the generated text.
     */
    async generate(prompt: string): Promise<string> {
        this.validateApiKey();

        const url = `https://generativelanguage.googleapis.com/v1beta/models/${this.model}:generateContent?key=${this.apiKey}`;
        const body: any = {
            contents: [{ parts: [{ text: prompt }] }]
        };

        // Add thinking configuration for supported models
        if (this.isThinkingSupported()) {
            body.generationConfig = {
                thinkingConfig: {
                    includeThoughts: true
                }
            };
        }

        const data = await this.makeRequest(url, body);

        // Handle content blocking
        if (data.promptFeedback?.blockReason) {
            throw new Error(`Gemini blocked request: ${data.promptFeedback.blockReason} - ${data.promptFeedback.blockReasonMessage || ''}`);
        }

        // Handle Gemini-specific response structure
        if (data.candidates?.[0]?.content?.parts) {
            const parts = data.candidates[0].content.parts;

            // Separate thinking and main content parts
            const thinkingParts: string[] = [];
            const mainParts: string[] = [];

            for (const part of parts) {
                // Check if part has text property (even if empty)
                if (part.hasOwnProperty('text')) {
                    if (part.thought) {
                        // This is thinking content
                        if (part.text) thinkingParts.push(part.text);
                    } else {
                        // This is main response content
                        if (part.text) mainParts.push(part.text);
                    }
                }
            }

            // Combine thinking and main content in the expected format
            let response = '';

            // Add thinking content wrapped in <think> tags if present
            if (thinkingParts.length > 0) {
                const thinkingContent = thinkingParts.join('\n\n');
                response += `<think>${thinkingContent}</think>`;
            }

            // Add main content
            if (mainParts.length > 0) {
                response += mainParts.join('\n\n');
            }

            // If we have any content, return it
            if (response) {
                return response;
            }

            // If we got here, the response structure was valid but contained no text content
            // This is the case you mentioned - API responded successfully but with empty text
            console.warn("Gemini API returned valid response structure but empty text content");
            return ""; // Return empty string - this will be handled as a warning in the service layer
        }

        throw new Error("Unexpected Gemini API response structure.");
    }
}
