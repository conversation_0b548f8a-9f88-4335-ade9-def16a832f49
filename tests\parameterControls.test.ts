import { describe, it, expect, beforeEach } from 'vitest';
import { get } from 'svelte/store';
import { appSettings, updateAppSetting, initializeAllSettings } from '../src/lib/stores/unifiedSettingsStore';

describe('Parameter Controls Integration', () => {
    beforeEach(async () => {
        // Reset the store before each test
        appSettings.set({
            fontFamily: 'Arial, sans-serif',
            fontSize: '16',
            textAlign: 'left',
            lineSpacing: '1.5',
            aiProvider: 'openai',
            aiModel: 'gpt-4o-mini',
            autocompleteContextLength: 1000,
            temperature: 0.7,
            topP: 0.9,
            sidebarVisible: true,
            darkMode: false,
            showThinkingPanel: true,
            systemPrompts: {
                autocomplete: "Default autocomplete prompt",
                replaceSelection: "Default replace selection prompt",
                insertAtCursor: "Default insert at cursor prompt"
            }
        });
        await initializeAllSettings();
    });

    it('should have temperature and topP in default settings', () => {
        const settings = get(appSettings);
        expect(settings.temperature).toBe(0.7);
        expect(settings.topP).toBe(0.9);
    });

    it('should update temperature setting', () => {
        updateAppSetting('temperature', 0.2);
        const settings = get(appSettings);
        expect(settings.temperature).toBe(0.2);
    });

    it('should update topP setting', () => {
        updateAppSetting('topP', 0.1);
        const settings = get(appSettings);
        expect(settings.topP).toBe(0.1);
    });

    it('should handle temperature boundary values', () => {
        // Test minimum value
        updateAppSetting('temperature', 0);
        expect(get(appSettings).temperature).toBe(0);

        // Test maximum value
        updateAppSetting('temperature', 2);
        expect(get(appSettings).temperature).toBe(2);

        // Test decimal value
        updateAppSetting('temperature', 1.5);
        expect(get(appSettings).temperature).toBe(1.5);
    });

    it('should handle topP boundary values', () => {
        // Test minimum value
        updateAppSetting('topP', 0.1);
        expect(get(appSettings).topP).toBe(0.1);

        // Test maximum value
        updateAppSetting('topP', 0.99);
        expect(get(appSettings).topP).toBe(0.99);

        // Test decimal value
        updateAppSetting('topP', 0.5);
        expect(get(appSettings).topP).toBe(0.5);
    });

    it('should maintain other settings when updating temperature/topP', () => {
        const originalSettings = get(appSettings);
        
        updateAppSetting('temperature', 0.3);
        updateAppSetting('topP', 0.2);
        
        const updatedSettings = get(appSettings);
        
        // Check that temperature and topP changed
        expect(updatedSettings.temperature).toBe(0.3);
        expect(updatedSettings.topP).toBe(0.2);
        
        // Check that other settings remained the same
        expect(updatedSettings.fontFamily).toBe(originalSettings.fontFamily);
        expect(updatedSettings.aiProvider).toBe(originalSettings.aiProvider);
        expect(updatedSettings.aiModel).toBe(originalSettings.aiModel);
        expect(updatedSettings.autocompleteContextLength).toBe(originalSettings.autocompleteContextLength);
    });

    it('should support template-like parameter combinations', () => {
        // Test Code Generation template (0.2, 0.1)
        updateAppSetting('temperature', 0.2);
        updateAppSetting('topP', 0.1);
        
        let settings = get(appSettings);
        expect(settings.temperature).toBe(0.2);
        expect(settings.topP).toBe(0.1);

        // Test Creative Writing template (0.7, 0.8)
        updateAppSetting('temperature', 0.7);
        updateAppSetting('topP', 0.8);
        
        settings = get(appSettings);
        expect(settings.temperature).toBe(0.7);
        expect(settings.topP).toBe(0.8);

        // Test Chatbot Responses template (0.5, 0.5)
        updateAppSetting('temperature', 0.5);
        updateAppSetting('topP', 0.5);
        
        settings = get(appSettings);
        expect(settings.temperature).toBe(0.5);
        expect(settings.topP).toBe(0.5);
    });
});
