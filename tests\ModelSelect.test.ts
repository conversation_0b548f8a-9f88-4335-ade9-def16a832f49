import { describe, it, expect } from 'vitest';
import type { ModelConfig } from '../src/lib/config';

// Test the model name parsing logic
function parseModelName(name: string): { modelName: string; description: string } {
  const parts = name.split(' - ');
  if (parts.length >= 2) {
    return {
      modelName: parts[0].trim(),
      description: parts.slice(1).join(' - ').trim()
    };
  }
  return {
    modelName: name,
    description: ''
  };
}

describe('ModelSelect', () => {
  const mockModels: ModelConfig[] = [
    { id: 'gpt-4o', name: 'GPT-4o - Flagship, Intelligent, Multimodal' },
    { id: 'gpt-4o-mini', name: 'GPT-4o mini - Fast, Cost-Effective, Multimodal' },
    { id: 'gemini-1.5-flash', name: 'Gemini 1.5 Flash - Fast, Efficient, Good Value' },
    { id: 'simple-model', name: 'Simple Model' } // Model without description
  ];

  it('parses model names with descriptions correctly', () => {
    const result = parseModelName('GPT-4o - Flagship, Intelligent, Multimodal');
    expect(result.modelName).toBe('GPT-4o');
    expect(result.description).toBe('Flagship, Intelligent, Multimodal');
  });

  it('parses model names without descriptions correctly', () => {
    const result = parseModelName('Simple Model');
    expect(result.modelName).toBe('Simple Model');
    expect(result.description).toBe('');
  });

  it('handles models with multiple dashes in description', () => {
    const result = parseModelName('Model - Part1 - Part2 - Part3');
    expect(result.modelName).toBe('Model');
    expect(result.description).toBe('Part1 - Part2 - Part3');
  });

  it('handles empty model names gracefully', () => {
    const result = parseModelName('');
    expect(result.modelName).toBe('');
    expect(result.description).toBe('');
  });

  it('handles model names with only dashes', () => {
    const result = parseModelName(' - ');
    expect(result.modelName).toBe('');
    expect(result.description).toBe('');
  });

  it('trims whitespace from parsed names and descriptions', () => {
    const result = parseModelName('  Model Name  -  Description Here  ');
    expect(result.modelName).toBe('Model Name');
    expect(result.description).toBe('Description Here');
  });
});
