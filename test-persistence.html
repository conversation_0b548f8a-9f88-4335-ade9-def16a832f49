<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Model Persistence Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        button { margin: 5px; padding: 10px; }
        .result { margin: 10px 0; padding: 10px; background: #f5f5f5; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
    </style>
</head>
<body>
    <h1>AI Model Persistence Test</h1>
    
    <div class="test-section">
        <h2>Current localStorage State</h2>
        <button onclick="showCurrentState()">Show Current State</button>
        <div id="currentState" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>Test Scenarios</h2>
        <button onclick="testScenario1()">Test: Set OpenAI GPT-4o, then Gemini Pro, then reload</button>
        <button onclick="testScenario2()">Test: Set Custom model, then OpenAI, then reload</button>
        <button onclick="clearStorage()">Clear All Storage</button>
        <div id="testResults" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>Manual Test</h2>
        <p>1. Open the main app in another tab: <a href="http://localhost:5174" target="_blank">http://localhost:5174</a></p>
        <p>2. Change AI provider and model</p>
        <p>3. Reload the page</p>
        <p>4. Check if the selections are preserved</p>
        <button onclick="showCurrentState()">Refresh State</button>
    </div>

    <script>
        function showCurrentState() {
            const unified = localStorage.getItem('aiNotepadSvelteUnifiedSettings');
            const div = document.getElementById('currentState');
            
            if (unified) {
                try {
                    const parsed = JSON.parse(unified);
                    div.innerHTML = `
                        <h3>Unified Settings:</h3>
                        <pre>${JSON.stringify(parsed, null, 2)}</pre>
                    `;
                    div.className = 'result success';
                } catch (e) {
                    div.innerHTML = `<strong>Error parsing unified settings:</strong> ${e.message}`;
                    div.className = 'result error';
                }
            } else {
                div.innerHTML = '<strong>No unified settings found in localStorage</strong>';
                div.className = 'result error';
            }
        }
        
        function testScenario1() {
            const testData = {
                appSettings: {
                    fontFamily: 'Arial, sans-serif',
                    fontSize: '16',
                    textAlign: 'left',
                    lineSpacing: '1.5',
                    aiProvider: 'gemini',
                    aiModel: 'gemini-1.5-pro',
                    autocompleteContextLength: 1000,
                    temperature: 0.7,
                    topP: 0.9,
                    sidebarVisible: true,
                    darkMode: false,
                    showThinkingPanel: true,
                    systemPrompts: {
                        autocomplete: 'Complete the following text naturally:',
                        replaceSelection: 'Replace the selected text with an improved version:',
                        insertAtCursor: 'Generate relevant content for insertion:'
                    }
                },
                apiKeys: {
                    gemini: '',
                    openai: '',
                    customUrl: '',
                    customKey: ''
                },
                notepadContent: '',
                customModelHistory: { gemini: [], openai: [], custom: [] },
                providerSpecificModels: {
                    gemini: 'gemini-1.5-pro',
                    openai: 'gpt-4o',
                    custom: ''
                }
            };
            
            localStorage.setItem('aiNotepadSvelteUnifiedSettings', JSON.stringify(testData));
            document.getElementById('testResults').innerHTML = `
                <div class="success">
                    <strong>Test Scenario 1 Applied:</strong><br>
                    - Set provider to Gemini<br>
                    - Set Gemini model to gemini-1.5-pro<br>
                    - Set OpenAI model to gpt-4o (for when switching)<br>
                    <br>
                    <strong>Now reload the main app and check if:</strong><br>
                    1. Provider shows as "Gemini"<br>
                    2. Model shows as "Gemini 1.5 Pro"<br>
                    3. When switching to OpenAI, it should show "GPT-4o"
                </div>
            `;
        }
        
        function testScenario2() {
            const testData = {
                appSettings: {
                    fontFamily: 'Arial, sans-serif',
                    fontSize: '16',
                    textAlign: 'left',
                    lineSpacing: '1.5',
                    aiProvider: 'openai',
                    aiModel: 'gpt-4o',
                    autocompleteContextLength: 1000,
                    temperature: 0.7,
                    topP: 0.9,
                    sidebarVisible: true,
                    darkMode: false,
                    showThinkingPanel: true,
                    systemPrompts: {
                        autocomplete: 'Complete the following text naturally:',
                        replaceSelection: 'Replace the selected text with an improved version:',
                        insertAtCursor: 'Generate relevant content for insertion:'
                    }
                },
                apiKeys: {
                    gemini: '',
                    openai: '',
                    customUrl: '',
                    customKey: ''
                },
                notepadContent: '',
                customModelHistory: { gemini: [], openai: [], custom: [{ model: 'my-custom-model', timestamp: Date.now() }] },
                providerSpecificModels: {
                    gemini: 'gemini-2.5-flash',
                    openai: 'gpt-4o',
                    custom: 'my-custom-model'
                }
            };
            
            localStorage.setItem('aiNotepadSvelteUnifiedSettings', JSON.stringify(testData));
            document.getElementById('testResults').innerHTML = `
                <div class="success">
                    <strong>Test Scenario 2 Applied:</strong><br>
                    - Set provider to OpenAI<br>
                    - Set OpenAI model to gpt-4o<br>
                    - Set custom model to my-custom-model<br>
                    - Set Gemini model to gemini-2.5-flash<br>
                    <br>
                    <strong>Now reload the main app and check if:</strong><br>
                    1. Provider shows as "OpenAI"<br>
                    2. Model shows as "GPT-4o"<br>
                    3. When switching to Custom, it should show "my-custom-model"<br>
                    4. When switching to Gemini, it should show "Gemini 2.5 Flash"
                </div>
            `;
        }
        
        function clearStorage() {
            localStorage.removeItem('aiNotepadSvelteUnifiedSettings');
            // Also clear any legacy keys
            const legacyKeys = [
                'aiNotepadSvelteContent',
                'aiNotepadSvelteApiKeys',
                'aiNotepadSvelteSettings',
                'aiNotepadSvelteCustomModelHistory',
                'aiNotepadSvelteProviderSpecificModels'
            ];
            legacyKeys.forEach(key => localStorage.removeItem(key));
            
            document.getElementById('testResults').innerHTML = `
                <div class="success">
                    <strong>Storage Cleared!</strong><br>
                    All localStorage data has been removed. Reload the main app to see default behavior.
                </div>
            `;
        }
        
        // Show current state on load
        showCurrentState();
    </script>
</body>
</html>
