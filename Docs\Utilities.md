# Utilities Documentation

## Overview

Utility functions provide pure, reusable functionality for text manipulation, AI response processing, and model management. All utilities have been simplified for clarity.

## Text Utils (`textUtils.ts`)

### Purpose
Handles text manipulation operations for AI features. **Important**: All text extraction functions preserve leading and trailing whitespace to maintain proper context for AI autocomplete operations.

### Core Functions

#### `getTextContext(text: string, maxLength?: number): string`
- Gets the last `maxLength` characters of a string.
- **@param** `text` The source string.
- **@param** `maxLength` The maximum number of characters to return from the end of the string.
- **@returns** The trailing text context.

#### `extractSelectionContext(fullText, selectionStart, selectionEnd, beforeContextLength, afterContextLength)`
- Extracts the text before, after, and within a selection.
- **@param** `fullText` The entire text content.
- **@param** `selectionStart` The starting index of the selection.
- **@param** `selectionEnd` The ending index of the selection.
- **@param** `beforeContextLength` The number of characters to extract before the selection.
- **@param** `afterContextLength` The number of characters to extract after the selection.
- **@returns** An object containing the text before, after, and inside the selection.

#### `extractCursorContext(fullText, cursorPosition, beforeContextLength, afterContextLength)`
- Extracts the text before and after the cursor position.
- **@param** `fullText` The entire text content.
- **@param** `cursorPosition` The current cursor position.
- **@param** `beforeContextLength` The number of characters to extract before the cursor.
- **@param** `afterContextLength` The number of characters to extract after the cursor.
- **@returns** An object containing the text before and after the cursor.

### Prompt Generation Functions

#### `createAutocompletePrompt(context: string, systemPrompt: string): string`
- Creates a prompt for the AI to continue writing from the given context.
- Uses custom system prompt for AI behavior guidance.
- **@param** `context` The text context to use for the prompt.
- **@param** `systemPrompt` The custom system prompt to use for autocomplete.
- **@returns** The formatted autocomplete prompt.

#### `createAnswerSelectionPrompt(beforeText: string, afterText: string, systemPrompt: string): string`
- Creates a prompt for the AI to fill in the text between two segments.
- Uses custom system prompt for AI behavior guidance.
- **@param** `beforeText` The text before the selection.
- **@param** `afterText` The text after the selection.
- **@param** `systemPrompt` The custom system prompt to use for replace selection.
- **@returns** The formatted Replace Selection prompt.

#### `createCursorInsertPrompt(beforeText: string, afterText: string, systemPrompt: string): string`
- Creates a prompt for the AI to insert text at the current cursor position.
- Uses custom system prompt for AI behavior guidance.
- **@param** `beforeText` The text before the cursor.
- **@param** `afterText` The text after the cursor.
- **@param** `systemPrompt` The custom system prompt to use for insert at cursor.
- **@returns** The formatted cursor insert prompt.

### Usage Patterns

All prompt functions follow the pattern:
1. Provide clear instructions to AI
2. Include relevant context
3. Specify output requirements
4. Maintain consistent style
5. **Explicitly instruct AI to respect whitespace** to prevent spacing issues

---

## Thinking Utils (`thinkingUtils.ts`)

### Purpose
Processes AI responses to extract and format thinking content. **Important**: The `processThinkingResponse` function preserves leading and trailing whitespace in the main content to maintain proper spacing for AI autocomplete operations.

### Core Interface

#### `ProcessedResponse`
- Represents the processed response from the AI, with thinking content separated.
- **`content`**: The main content of the response, without thinking tags.
- **`thinking`**: The extracted thinking content, if any.
- **`hasThinking`**: A flag indicating whether thinking content was found.

### Core Functions

#### `processThinkingResponse(rawResponse: string): ProcessedResponse`
- Processes a raw AI response to extract thinking content and clean the output.
- **@param** `rawResponse` The raw response string from the AI.
- **@returns** A `ProcessedResponse` object with the content and thinking separated.

**Process:**
1. Searches for `<think>...</think>` tags (case insensitive)
2. Extracts all thinking content
3. Removes thinking tags from response
4. Returns processed object

**Edge Cases:**
- Empty response: Returns empty processed response
- No thinking tags: Returns content as-is with no thinking
- Multiple thinking sections: Combines with separator
- Malformed tags: Ignores and processes normally

#### `formatThinkingContent(thinking: string): string`
- Formats the thinking content for display by cleaning up whitespace and paragraphs.
- **@param** `thinking` The raw thinking string.
- **@returns** The formatted thinking string.

**Process:**
1. Splits into paragraphs
2. Trims whitespace
3. Filters empty paragraphs
4. Rejoins with consistent spacing

### Usage in Application

The thinking system allows users to see AI reasoning:
1. AI responses can include thinking in `<think>` tags
2. Thinking is extracted and stored separately
3. Users can view thinking for any AI-generated text
4. Thinking panel shows the reasoning process

---

## Model Utils (`modelUtils.ts`)

### Purpose
Provides utilities for model management and validation.

### Core Functions

#### `isCustomModel(model: string): boolean`
- Checks if a model is a custom model (i.e., not in the predefined lists).
- **@param** `model` The model ID to check.
- **@returns** `true` if the model is custom, `false` otherwise.

#### `getModelDescription(provider: string, modelId: string): string | null`
- Gets the description of a model for a specific provider.
- **@param** `provider` The AI provider.
- **@param** `modelId` The model ID.
- **@returns** The model description, or `null` if not found.

#### `getAvailableModels(provider: string): ModelConfig[]`
- Gets all available models for a specific provider.
- **@param** `provider` The AI provider.
- **@returns** An array of available models.

### Model Classification

The system distinguishes between:

1. **Predefined Models**
   - Listed in configuration
   - Have descriptions and metadata
   - Validated for availability

2. **Custom Models**
   - User-entered model names
   - No predefined metadata
   - Stored in history for reuse

### Integration with Stores

Model utilities work with the unified settings store:
- Custom model detection for history management
- Model validation during provider switching
- Description lookup for UI display

---

## AI Utils (`aiUtils.ts`)

### Purpose
Provides common utilities for AI operations.

### Core Functions

#### `fetchWithTimeout(url: string, options: RequestInit, timeout?: number): Promise<Response>`
- Fetches a resource with a specified timeout.
- **@param** `resource` The resource to fetch.
- **@param** `options` The options for the fetch request.
- **@param** `timeout` The timeout in milliseconds.
- **@returns** A promise that resolves to the response.
- **@throws** Will throw an error if the fetch request times out.

### Usage in Adapters

All AI adapters use this utility for:
- Consistent timeout behavior
- Standardized error handling
- Reliable request cancellation
- Uniform response processing

---

## Common Patterns

### Error Handling
All utilities follow consistent error handling:
- Validate inputs
- Provide meaningful error messages
- Handle edge cases gracefully
- Return sensible defaults when possible

### Pure Functions
Most utilities are pure functions:
- No side effects
- Predictable outputs
- Easy to test
- Composable

### Type Safety
All utilities use TypeScript:
- Strong typing for parameters
- Clear return types
- Interface definitions
- Compile-time validation

### Performance
Utilities are optimized for:
- Minimal memory allocation
- Efficient string operations
- Fast execution
- Reusability
