# Services Documentation

## Overview

Services handle business logic, external API communication, and data persistence. All services have been simplified with clear responsibilities.

## API Service (`apiService.ts`)

### Purpose
Orchestrates AI API calls with provider abstraction and error handling.

### Dependencies
- AI adapters (Gemini, OpenAI, Custom)
- Unified settings store
- Status store for user feedback
- Thinking utilities for response processing

### Core Functions

#### `callAIWithThinking(prompt: string): Promise<ProcessedResponse | null>`
Main function for AI interactions:

1. **Validation Phase**
   - Gets current settings from store
   - Validates model selection
   - Ensures required API keys are present

2. **Adapter Creation**
   - Creates appropriate adapter based on provider
   - Passes credentials and model configuration
   - Handles provider-specific requirements

3. **API Call**
   - Makes request through adapter
   - Shows status updates to user
   - Handles loading states

4. **Response Processing**
   - Extracts thinking content if present
   - Returns processed response object
   - Handles errors gracefully

### Helper Functions

#### `validateModel(provider: AIProviderType, modelId: string): string`
- Validates the model selection for the current provider.
- **@param** `provider` The AI provider.
- **@param** `modelId` The selected model ID.
- **@returns** The validated model ID.

#### `createAdapter(provider: AIProviderType, apiKeys: ApiKeys, model: string): AIProviderInterface`
- Creates an AI adapter based on the specified provider.
- **@param** `provider` The AI provider.
- **@param** `apiKeys` The API keys.
- **@param** `model` The selected model.
- **@returns** An instance of an AI provider adapter.

### Error Handling
- User-friendly error messages
- Detailed logging for debugging
- Graceful degradation on failures
- Status updates for user feedback

### Provider Support
- **OpenAI**: Direct API or compatible endpoints
- **Gemini**: Google's Generative AI API
- **Custom**: Any OpenAI-compatible API

---

## Storage Service (`storageService.ts`)

### Purpose
Handles data persistence to localStorage with unified settings management.

### Core Interface

#### `UnifiedSettings`
Complete application state structure:
```typescript
{
  appSettings: AppSettings;
  apiKeys: ApiKeys;
  notepadContent: string;
  customModelHistory: CustomModelHistory;
  lastCustomModels: Record<AIProviderType, string>;
  lastSelectedModels: LastSelectedModels;
}
```

### Core Functions

#### `loadUnifiedSettings(): UnifiedSettings`
- Loads all settings from localStorage.
- Merges with defaults for missing properties.
- Falls back to individual legacy keys for migration and cleans them up.
- Returns a complete settings object.

#### `saveUnifiedSettings(settings: UnifiedSettings): boolean`
- Saves unified settings to localStorage.
- Returns success status.
- Handles storage errors gracefully.

### Helper Functions

#### `createDefaultSettings(): UnifiedSettings`
- Creates a default settings structure.
- Used when no stored settings exist.
- Ensures all properties are present.

### Storage Strategy

#### Unified Storage
- Primary storage in single key
- Reduces localStorage operations
- Atomic updates for consistency

#### Individual Key Fallback
- Supports migration from old versions
- Provides backup if unified storage fails
- Maintains backward compatibility

#### Error Handling
- Safe JSON parsing with fallbacks
- Graceful handling of storage quota
- Continues operation if storage fails

### Browser Compatibility
- Checks for browser environment
- Returns defaults in SSR context
- Handles localStorage unavailability

---

## AI Adapters

### Base Adapter (`aiProvider.ts`)

#### Purpose
Provides common functionality for all AI providers.

#### Features
- Common error handling patterns
- Request/response processing
- Timeout management
- Response validation

#### Core Methods

##### `generate(prompt: string): Promise<string>`
- Generates a response from the AI provider.
- **@param** `prompt` The prompt to send to the AI.
- **@returns** A promise that resolves to the generated text.

##### `validateApiKey()`
- Validates the API key. This method can be overridden by subclasses.
- **@throws** Will throw an error if the API key is missing.

##### `getRequestHeaders(): Record<string, string>`
- Gets the base request headers.
- **@returns** The base request headers.

##### `handleApiError(response: Response, responseText: string): never`
- Handles API errors.
- **@param** `response` The response object from the fetch call.
- **@param** `responseText` The response text.
- **@throws** Will throw an error with a descriptive message.

##### `makeRequest(url: string, body: any, headers?: Record<string, string>): Promise<any>`
- Makes an API request.
- **@param** `url` The URL to send the request to.
- **@param** `body` The request body.
- **@param** `headers` The request headers.
- **@returns** A promise that resolves to the response data.

### Gemini Adapter (`geminiAdapter.ts`)

#### Purpose
Adapter for the Google Gemini API with native thinking support for 2.5 series models.

#### Thinking Implementation
- **Model Detection**: Automatically detects Gemini 2.5 series models
- **Request Configuration**: Adds `thinkingConfig: { includeThoughts: true }` for supported models
- **Response Processing**: Separates thinking parts (`part.thought: true`) from main content
- **Format Compatibility**: Wraps thinking content in `<think>` tags for existing UI compatibility
- **Seamless Integration**: Works with existing thinking utilities without UI changes

#### Core Methods
##### `generate(prompt: string): Promise<string>`
- Generates a response from the Gemini API.
- Automatically enables thinking for Gemini 2.5 series models
- Extracts and formats thinking content with `<think>` tags for compatibility
- **@param** `prompt` The prompt to send to the AI.
- **@returns** A promise that resolves to the generated text (with thinking content if available).

##### `isThinkingSupported(): boolean` (private)
- Checks if the current model supports thinking capabilities.
- **@returns** `true` for Gemini 2.5 series models, `false` otherwise.

### OpenAI Adapter (`openAIAdapter.ts`)

#### Purpose
Adapter for the OpenAI API and compatible endpoints.

#### Core Methods
##### `generate(prompt: string): Promise<string>`
- Generates a response from the OpenAI API.
- **@param** `prompt` The prompt to send to the AI.
- **@returns** A promise that resolves to the generated text.

##### `buildUrl(): string`
- Builds the appropriate URL for OpenAI or custom endpoints.
- **@returns** The URL for the API request.

##### `buildHeaders(): Record<string, string>`
- Builds the headers for the API request.
- **@returns** The headers for the API request.

### Custom Adapter (`customAdapter.ts`)

#### Purpose
Adapter for custom OpenAI-compatible endpoints.

#### Core Methods
##### `constructor(apiKey: string | null, model: string, customUrl: string)`
- Creates an instance of CustomAdapter.
- **@param** `apiKey` The API key for the custom provider, if any.
- **@param** `model` The model to use for generation.
- **@param** `customUrl` The custom URL for the provider.

##### `validateApiKey(): void`
- Overrides the API key validation to allow for custom APIs that may not require authentication.

### Common Patterns

#### Error Handling
All adapters handle:
- Network timeouts
- Invalid responses
- HTML error pages
- Authentication failures
- Model availability issues

#### Response Processing
- Validates JSON responses
- Extracts content from provider-specific formats
- Handles empty or malformed responses
- Provides consistent error messages

#### Configuration
- Provider-specific URL construction
- Authentication header management
- Custom parameter handling
- Timeout and retry logic
