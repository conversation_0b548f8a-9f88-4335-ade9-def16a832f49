import { describe, it, expect, vi, beforeEach } from 'vitest';
import { GeminiAdapter } from '../src/lib/ai/geminiAdapter';

// Mock the fetchWithTimeout function
vi.mock('../src/lib/ai/aiUtils', () => ({
    fetchWithTimeout: vi.fn()
}));

describe('GeminiAdapter', () => {
    let adapter: GeminiAdapter;
    const mockApiKey = 'test-api-key';
    
    beforeEach(() => {
        vi.clearAllMocks();
    });

    describe('Thinking Support Detection', () => {
        it('should detect thinking support for Gemini 2.5 models', () => {
            const thinkingModels = [
                'gemini-2.5-flash',
                'gemini-2.5-pro',
                'gemini-2.5-flash-preview-04-17',
                'gemini-2.5-pro-preview-05-06'
            ];

            thinkingModels.forEach(model => {
                adapter = new GeminiAdapter(mockApiKey, model);
                // Access private method through type assertion for testing
                const isSupported = (adapter as any).isThinkingSupported();
                expect(isSupported).toBe(true);
            });
        });

        it('should not detect thinking support for non-2.5 models', () => {
            const nonThinkingModels = [
                'gemini-1.5-flash',
                'gemini-1.5-pro',
                'gemini-1.0-pro',
                'gemini-2.0-flash'
            ];

            nonThinkingModels.forEach(model => {
                adapter = new GeminiAdapter(mockApiKey, model);
                const isSupported = (adapter as any).isThinkingSupported();
                expect(isSupported).toBe(false);
            });
        });
    });

    describe('Response Processing', () => {
        beforeEach(() => {
            adapter = new GeminiAdapter(mockApiKey, 'gemini-2.5-pro');
        });

        it('should format thinking content with <think> tags', async () => {
            const mockResponse = {
                candidates: [{
                    content: {
                        parts: [
                            { text: 'This is my thinking process', thought: true },
                            { text: 'This is the main response', thought: false }
                        ]
                    }
                }]
            };

            // Mock the makeRequest method
            vi.spyOn(adapter as any, 'makeRequest').mockResolvedValue(mockResponse);

            const result = await adapter.generate('test prompt');
            
            expect(result).toBe('<think>This is my thinking process</think>This is the main response');
        });

        it('should handle multiple thinking parts', async () => {
            const mockResponse = {
                candidates: [{
                    content: {
                        parts: [
                            { text: 'First thinking part', thought: true },
                            { text: 'Second thinking part', thought: true },
                            { text: 'Main response', thought: false }
                        ]
                    }
                }]
            };

            vi.spyOn(adapter as any, 'makeRequest').mockResolvedValue(mockResponse);

            const result = await adapter.generate('test prompt');
            
            expect(result).toBe('<think>First thinking part\n\nSecond thinking part</think>Main response');
        });

        it('should handle response without thinking content', async () => {
            const mockResponse = {
                candidates: [{
                    content: {
                        parts: [
                            { text: 'Just a regular response', thought: false }
                        ]
                    }
                }]
            };

            vi.spyOn(adapter as any, 'makeRequest').mockResolvedValue(mockResponse);

            const result = await adapter.generate('test prompt');
            
            expect(result).toBe('Just a regular response');
        });

        it('should handle response with only thinking content', async () => {
            const mockResponse = {
                candidates: [{
                    content: {
                        parts: [
                            { text: 'Only thinking content', thought: true }
                        ]
                    }
                }]
            };

            vi.spyOn(adapter as any, 'makeRequest').mockResolvedValue(mockResponse);

            const result = await adapter.generate('test prompt');
            
            expect(result).toBe('<think>Only thinking content</think>');
        });
    });

    describe('Request Configuration', () => {
        it('should include thinking config for supported models', async () => {
            adapter = new GeminiAdapter(mockApiKey, 'gemini-2.5-pro');
            
            const mockMakeRequest = vi.spyOn(adapter as any, 'makeRequest').mockResolvedValue({
                candidates: [{ content: { parts: [{ text: 'response', thought: false }] } }]
            });

            await adapter.generate('test prompt');

            expect(mockMakeRequest).toHaveBeenCalledWith(
                expect.stringContaining('gemini-2.5-pro:generateContent'),
                expect.objectContaining({
                    contents: [{ parts: [{ text: 'test prompt' }] }],
                    generationConfig: {
                        thinkingConfig: {
                            includeThoughts: true
                        }
                    }
                })
            );
        });

        it('should not include thinking config for unsupported models', async () => {
            adapter = new GeminiAdapter(mockApiKey, 'gemini-1.5-pro');
            
            const mockMakeRequest = vi.spyOn(adapter as any, 'makeRequest').mockResolvedValue({
                candidates: [{ content: { parts: [{ text: 'response', thought: false }] } }]
            });

            await adapter.generate('test prompt');

            expect(mockMakeRequest).toHaveBeenCalledWith(
                expect.stringContaining('gemini-1.5-pro:generateContent'),
                expect.objectContaining({
                    contents: [{ parts: [{ text: 'test prompt' }] }]
                })
            );

            const requestBody = mockMakeRequest.mock.calls[0][1];
            expect(requestBody).not.toHaveProperty('generationConfig');
        });
    });

    describe('Error Handling', () => {
        beforeEach(() => {
            adapter = new GeminiAdapter(mockApiKey, 'gemini-2.5-pro');
        });

        it('should handle content blocking', async () => {
            const mockResponse = {
                promptFeedback: {
                    blockReason: 'SAFETY',
                    blockReasonMessage: 'Content blocked for safety'
                }
            };

            vi.spyOn(adapter as any, 'makeRequest').mockResolvedValue(mockResponse);

            await expect(adapter.generate('test prompt')).rejects.toThrow(
                'Gemini blocked request: SAFETY - Content blocked for safety'
            );
        });

        it('should handle empty response content gracefully (no parts)', async () => {
            const mockResponse = {
                candidates: [{
                    content: {
                        parts: []
                    }
                }]
            };

            vi.spyOn(adapter as any, 'makeRequest').mockResolvedValue(mockResponse);

            const result = await adapter.generate('test prompt');
            expect(result).toBe('');
        });

        it('should handle empty text content gracefully (empty text property)', async () => {
            // This is the case you mentioned - valid structure but empty text
            const mockResponse = {
                candidates: [{
                    content: {
                        parts: [{
                            text: ""
                        }],
                        role: "model"
                    },
                    finishReason: "STOP"
                }],
                usageMetadata: {
                    promptTokenCount: 345,
                    totalTokenCount: 345
                },
                modelVersion: "gemini-2.0-flash"
            };

            vi.spyOn(adapter as any, 'makeRequest').mockResolvedValue(mockResponse);

            const result = await adapter.generate('test prompt');
            expect(result).toBe('');
        });

        it('should handle unexpected response structure', async () => {
            const mockResponse = {};

            vi.spyOn(adapter as any, 'makeRequest').mockResolvedValue(mockResponse);

            await expect(adapter.generate('test prompt')).rejects.toThrow(
                'Unexpected Gemini API response structure.'
            );
        });
    });

    describe('Integration with Thinking Utils', () => {
        it('should produce output compatible with processThinkingResponse', async () => {
            adapter = new GeminiAdapter(mockApiKey, 'gemini-2.5-pro');

            const mockResponse = {
                candidates: [{
                    content: {
                        parts: [
                            { text: 'Let me think about this step by step...', thought: true },
                            { text: 'The answer is 42.', thought: false }
                        ]
                    }
                }]
            };

            vi.spyOn(adapter as any, 'makeRequest').mockResolvedValue(mockResponse);

            const result = await adapter.generate('What is the answer?');

            // The result should be in the format expected by processThinkingResponse
            expect(result).toBe('<think>Let me think about this step by step...</think>The answer is 42.');

            // Verify it contains thinking tags that can be processed
            expect(result).toMatch(/<think>.*<\/think>/);
            expect(result).toContain('Let me think about this step by step...');
            expect(result).toContain('The answer is 42.');
        });
    });
});
