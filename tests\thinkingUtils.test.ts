/**
 * Tests for thinking utility functions
 * Run with: npm test -- thinkingUtils.test.ts
 */

import { processThinkingResponse, formatThinkingContent } from '../src/lib/utils/thinkingUtils';

// Simple test runner
function test(name: string, fn: () => void) {
  try {
    fn();
    console.log(`✅ ${name}`);
  } catch (error) {
    console.log(`❌ ${name}: ${error instanceof Error ? error.message : String(error)}`);
  }
}

function assertEqual(actual: any, expected: any, message?: string) {
  if (actual !== expected) {
    throw new Error(`Expected "${expected}", got "${actual}"${message ? ': ' + message : ''}`);
  }
}

console.log('\n🧪 Running thinking utility tests...\n');

// Test processThinkingResponse
test('processThinkingResponse - no thinking tags', () => {
  const response = "  Hello world  ";
  const result = processThinkingResponse(response);
  
  assertEqual(result.content, "  Hello world  ");
  assertEqual(result.thinking, null);
  assertEqual(result.hasThinking, false);
});

test('processThinkingResponse - with thinking tags preserves spacing', () => {
  const response = "<think>Some thinking</think>  Hello world  ";
  const result = processThinkingResponse(response);
  
  assertEqual(result.content, "  Hello world  ");
  assertEqual(result.thinking, "Some thinking");
  assertEqual(result.hasThinking, true);
});

test('processThinkingResponse - multiple thinking blocks', () => {
  const response = "<think>First thought</think>  Content with spaces  <think>Second thought</think>";
  const result = processThinkingResponse(response);
  
  assertEqual(result.content, "  Content with spaces  ");
  assertEqual(result.thinking, "First thought\n\n---\n\nSecond thought");
  assertEqual(result.hasThinking, true);
});

test('processThinkingResponse - preserves leading and trailing spaces', () => {
  const response = "<think>Thinking</think>   Leading and trailing spaces   ";
  const result = processThinkingResponse(response);
  
  assertEqual(result.content, "   Leading and trailing spaces   ");
  assertEqual(result.thinking, "Thinking");
  assertEqual(result.hasThinking, true);
});

test('formatThinkingContent - basic formatting', () => {
  const thinking = "First paragraph\n\nSecond paragraph";
  const result = formatThinkingContent(thinking);
  
  assertEqual(result, "First paragraph\n\nSecond paragraph");
});

console.log('\n✨ All thinking utility tests completed!\n');
