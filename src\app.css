/* CSS Custom Properties for Light and Dark Themes */
:root {
    /* Light theme colors */
    --color-text-primary: #333;
    --color-background: #f0f0f0;
    --color-notepad-bg: #fafafa;
    --color-sidebar-bg: #e9e9e9;
    --color-header-bg: #333;
    --color-accent: #4CAF50;
    --color-selection: #b3d4fc;
    --color-ai-generated: #e6f7ff;
    --color-success: #d4edda;
    --color-error: #f8d7da;
    --color-info: #d1ecf1;
    --color-border: #ccc;
    --color-input-bg: #fff;
    --color-modal-backdrop: rgba(0, 0, 0, 0.5);
    --color-modal-bg: #fff;
    --color-modal-header: #f8f9fa;
    --color-button-secondary: #fff;
    --color-button-secondary-border: #ccc;
    --color-toggle-bg: #d0d0d0;
    --color-toggle-hover: #f0f0f0;
    --color-toggle-active: #b0b0b0;
    --color-toggle-arrow: #666;
    --color-shadow: rgba(0, 0, 0, 0.1);
}

/* Dark theme colors */
:root.dark {
    --color-text-primary: #e0e0e0;
    --color-background: #1a1a1a;
    --color-notepad-bg: #2d2d2d;
    --color-sidebar-bg: #252525;
    --color-header-bg: #1f1f1f;
    --color-accent: #4CAF50;
    --color-selection: #4a90e2;
    --color-ai-generated: #1e3a5f;
    --color-success: #2d5a3d;
    --color-error: #5a2d2d;
    --color-info: #2d4a5a;
    --color-border: #444;
    --color-input-bg: #3a3a3a;
    --color-modal-backdrop: rgba(0, 0, 0, 0.7);
    --color-modal-bg: #2d2d2d;
    --color-modal-header: #1f1f1f;
    --color-button-secondary: #3a3a3a;
    --color-button-secondary-border: #555;
    --color-toggle-bg: #3a3a3a;
    --color-toggle-hover: #4a4a4a;
    --color-toggle-active: #5a5a5a;
    --color-toggle-arrow: #ccc;
    --color-shadow: rgba(0, 0, 0, 0.3);
}

html, body {
    margin: 0;
    padding: 0;
    height: 100%;
    width: 100%;
    font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    background-color: var(--color-background);
    color: var(--color-text-primary);
    overflow: hidden;
    transition: background-color 0.2s ease, color 0.2s ease;
}

* {
    box-sizing: border-box;
}

::selection {
  background-color: var(--color-selection);
  color: var(--color-text-primary);
}

/* For Firefox */
::-moz-selection {
  background-color: var(--color-selection);
  color: var(--color-text-primary);
}

:global(.notepad-editor .ai-generated) {
  background-color: #e6f7ff; /* Example: light blue background */
  /* border: 1px dashed #91d5ff; */
  padding: 1px 3px;
  border-radius: 3px;
  /* You can add more distinct styling here */
}

/* If you want AI text to have a different selection color */
/* Note: Styling ::selection based on parent class is not universally supported */
/* This might only work if the selection is *entirely* within the .ai-generated span */
:global(.notepad-editor .ai-generated::selection) {
  background-color: #ffcc80; /* Example: orange selection for AI text */
  color: black;
}
:global(.notepad-editor .ai-generated::-moz-selection) {
  background-color: #ffcc80;
  color: black;
}